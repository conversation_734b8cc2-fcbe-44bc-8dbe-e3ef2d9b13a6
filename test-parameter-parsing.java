// 测试参数解析的代码片段

import com.google.gson.Gson;
import com.yxt.order.assistant.server.knowledge.swagger.dto.SwaggerDoc;

public class TestParameterParsing {
    
    public static void main(String[] args) {
        String json = """
        {
            "in": "body",
            "name": "req",
            "description": "req",
            "required": true,
            "schema": {
                "$ref": "#/definitions/DeleteDocumentReq"
            }
        }
        """;
        
        Gson gson = new Gson();
        SwaggerDoc.Parameter param = gson.fromJson(json, SwaggerDoc.Parameter.class);
        
        System.out.println("参数名: " + param.getName());
        System.out.println("位置: " + param.getIn());
        System.out.println("必填: " + param.isRequired());
        System.out.println("描述: " + param.getDescription());
        
        if (param.getSchema() != null) {
            System.out.println("Schema存在");
            System.out.println("Schema ref: " + param.getSchema().getRef());
            System.out.println("Schema type: " + param.getSchema().getType());
        } else {
            System.out.println("Schema为null");
        }
        
        // 测试extractRefName方法
        if (param.getSchema() != null && param.getSchema().getRef() != null) {
            String ref = param.getSchema().getRef();
            String className = ref.startsWith("#/definitions/") ? 
                ref.substring("#/definitions/".length()) : ref;
            System.out.println("提取的类名: " + className);
        }
    }
}

// 预期输出:
// 参数名: req
// 位置: body
// 必填: true
// 描述: req
// Schema存在
// Schema ref: #/definitions/DeleteDocumentReq
// Schema type: null
// 提取的类名: DeleteDocumentReq
