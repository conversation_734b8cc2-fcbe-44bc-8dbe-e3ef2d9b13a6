# 参数类型解析问题修复说明

## 问题描述

在Swagger转Markdown的过程中，遇到以下问题：

### 原始JSON数据
```json
{
    "in": "body",
    "name": "req", 
    "description": "req",
    "required": true,
    "schema": {
        "$ref": "#/definitions/DeleteDocumentReq"
    }
}
```

### 期望输出
```markdown
| 参数名 | 位置 | 类型 | 必填 | 描述 |
|--------|------|------|------|------|
| req | body | DeleteDocumentReq | 是 | req |
```

### 实际输出（修复前）
```markdown
| 参数名 | 位置 | 类型 | 必填 | 描述 |
|--------|------|------|------|------|
| req | body | Object | 是 | req |
```

## 问题根因

### 1. **JSON解析问题**
- 使用Gson解析JSON时，`@JsonProperty("$ref")`注解不被识别
- 需要同时添加`@SerializedName("$ref")`注解才能正确解析

### 2. **字段映射问题**
- `$ref`是JSON中的特殊字段名，在Java中不能直接作为字段名
- 需要使用注解将JSON字段映射到Java字段

## 修复方案

### 1. **添加Gson注解**
```java
@Data
public static class Schema {
    private String type;
    private String format;
    @JsonProperty("$ref")      // Jackson注解
    @SerializedName("$ref")    // Gson注解
    private String ref;
    private Schema items;
    private Map<String, Object> additionalProperties;
}
```

### 2. **增强调试日志**
```java
private String getParameterType(SwaggerDoc.Parameter param) {
    log.info("解析参数类型 - 参数名: {}, 位置: {}", param.getName(), param.getIn());
    
    if (param.getSchema() != null) {
        log.info("Schema存在 - ref: {}, type: {}", 
            param.getSchema().getRef(), param.getSchema().getType());
    } else {
        log.info("Schema为null");
    }
    
    // 处理schema引用类型
    if (param.getSchema() != null && param.getSchema().getRef() != null) {
        String refType = extractRefName(param.getSchema().getRef());
        log.info("找到schema引用类型: {}", refType);
        return refType;
    }
    
    // ... 其他处理逻辑
}
```

### 3. **完善类型解析逻辑**
```java
private String extractRefName(String ref) {
    if (ref != null && ref.startsWith("#/definitions/")) {
        return ref.substring("#/definitions/".length());
    }
    return ref;
}
```

## 修复效果

### 修复后的输出
```markdown
| 参数名 | 位置 | 类型 | 必填 | 描述 |
|--------|------|------|------|------|
| req | body | DeleteDocumentReq | 是 | req |

**DeleteDocumentReq 字段详情**:

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| documentId | string | 是 | 文档ID |
| knowledgeGroupId | integer(int64) | 是 | 知识库组ID |
```

## 验证方法

### 1. **查看日志输出**
启用INFO级别日志，查看参数解析过程：
```
解析参数类型 - 参数名: req, 位置: body
Schema存在 - ref: #/definitions/DeleteDocumentReq, type: null
找到schema引用类型: DeleteDocumentReq
```

### 2. **检查生成的Markdown**
- 参数类型列显示具体的Java类名
- 自动展开POJO类的字段详情
- 正确标识必填字段

### 3. **API测试**
```bash
POST /knowledge/start-pull-swagger
{
  "knowledgeGroupId": 1,
  "swaggerUrl": "http://order-service.svc.k8s.test.hxyxt.com/v2/api-docs"
}
```

## 技术要点

### 1. **JSON解析库兼容性**
- Jackson使用`@JsonProperty`
- Gson使用`@SerializedName`
- 同时添加两个注解确保兼容性

### 2. **特殊字段名处理**
- `$ref`在JSON中是合法的，但在Java中需要映射
- 使用`ref`作为Java字段名，通过注解映射

### 3. **调试策略**
- 添加详细的日志输出
- 分步骤验证解析过程
- 确保每个环节都能正确处理

## 总结

通过添加Gson注解和增强调试日志，成功解决了参数类型解析问题。现在能够正确识别POJO类型并生成详细的字段信息，大大提升了生成文档的可用性。
