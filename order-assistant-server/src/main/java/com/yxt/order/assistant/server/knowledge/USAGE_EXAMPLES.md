# 数据库表信息导出功能使用示例

## 1. 动态数据源模式（推荐）

### 1.1 基本使用

通过API传入数据源信息，无需在配置文件中预配置：

```bash
curl -X POST http://localhost:8080/knowledge/start-pull-database \
  -H "Content-Type: application/json" \
  -d '{
    "knowledgeGroupId": 1,
    "dataSources": [
      {
        "name": "订单数据库",
        "url": "**********************************************************************************",
        "username": "root",
        "password": "password123"
      },
      {
        "name": "用户数据库", 
        "url": "*********************************************************************************",
        "username": "root",
        "password": "password456"
      }
    ],
    "parallelProcessing": true,
    "includeSystemTables": false
  }'
```

### 1.2 高级配置

```bash
curl -X POST http://localhost:8080/knowledge/start-pull-database \
  -H "Content-Type: application/json" \
  -d '{
    "knowledgeGroupId": 1,
    "dataSources": [
      {
        "name": "生产数据库",
        "url": "*****************************************************************************",
        "username": "readonly_user",
        "password": "secure_password",
        "driverClassName": "com.mysql.cj.jdbc.Driver",
        "connectionTimeout": 30000,
        "maximumPoolSize": 3,
        "minimumIdle": 1,
        "testConnection": true
      }
    ],
    "parallelProcessing": false,
    "tableNamePattern": "order_*",
    "includeSystemTables": false,
    "connectionTimeoutSeconds": 30
  }'
```

### 1.3 测试数据源连接

在正式导出前，可以先测试连接：

```bash
curl -X POST http://localhost:8080/database-test/test-connection \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试数据库",
    "url": "************************************************",
    "username": "root",
    "password": "password"
  }'
```

### 1.4 获取表信息（不保存到知识库）

```bash
curl -X POST http://localhost:8080/database-test/tables-dynamic \
  -H "Content-Type: application/json" \
  -d '{
    "dataSources": [
      {
        "name": "测试数据库",
        "url": "************************************************",
        "username": "root",
        "password": "password"
      }
    ]
  }'
```

### 1.5 导出Markdown（不保存到知识库）

```bash
curl -X POST http://localhost:8080/database-test/markdown-dynamic \
  -H "Content-Type: application/json" \
  -d '{
    "dataSources": [
      {
        "name": "测试数据库",
        "url": "************************************************",
        "username": "root",
        "password": "password"
      }
    ],
    "tableNamePattern": "user_*"
  }'
```

## 2. 配置文件数据源模式（向后兼容）

### 2.1 使用配置文件中的数据源

```bash
curl -X POST http://localhost:8080/knowledge/start-pull-database \
  -H "Content-Type: application/json" \
  -d '{
    "knowledgeGroupId": 1,
    "useConfiguredDataSources": true,
    "includeSystemTables": false
  }'
```

### 2.2 测试配置文件数据源

```bash
# 获取表信息
curl http://localhost:8080/database-test/tables

# 导出Markdown
curl http://localhost:8080/database-test/markdown
```

## 3. 监控和管理

### 3.1 查看连接池状态

```bash
curl http://localhost:8080/database-test/connection-stats
```

## 4. 参数说明

### DataSourceConfig 参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| name | String | 是 | - | 数据源名称/别名 |
| url | String | 是 | - | 数据库连接URL |
| username | String | 是 | - | 用户名 |
| password | String | 是 | - | 密码 |
| driverClassName | String | 否 | com.mysql.cj.jdbc.Driver | 驱动类名 |
| connectionTimeout | Integer | 否 | 30000 | 连接超时时间（毫秒） |
| maximumPoolSize | Integer | 否 | 5 | 最大连接数 |
| minimumIdle | Integer | 否 | 1 | 最小空闲连接数 |
| testConnection | Boolean | 否 | true | 是否测试连接 |

### StartPullDatabaseReq 参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| knowledgeGroupId | Long | 是 | - | 知识库组ID |
| dataSources | List<DataSourceConfig> | 否 | null | 动态数据源配置列表 |
| useConfiguredDataSources | Boolean | 否 | false | 是否使用配置文件数据源 |
| parallelProcessing | Boolean | 否 | true | 是否并行处理多个数据源 |
| includeSystemTables | Boolean | 否 | false | 是否包含系统表 |
| tableNamePattern | String | 否 | null | 表名过滤模式（支持*通配符） |
| connectionTimeoutSeconds | Integer | 否 | 30 | 连接超时时间（秒） |

## 5. 常见问题

### 5.1 连接失败

如果连接失败，请检查：
- 数据库地址和端口是否正确
- 用户名和密码是否正确
- 数据库是否允许远程连接
- 防火墙设置
- MySQL驱动版本兼容性

### 5.2 权限问题

确保数据库用户有以下权限：
```sql
GRANT SELECT ON information_schema.* TO 'username'@'%';
GRANT SELECT ON your_database.* TO 'username'@'%';
```

### 5.3 性能优化

- 对于大型数据库，建议设置 `tableNamePattern` 过滤表
- 使用 `parallelProcessing: false` 避免对数据库造成过大压力
- 调整连接池参数 `maximumPoolSize` 和 `minimumIdle`

## 6. 生成的Markdown示例

```markdown
# 数据库表信息文档

## 目录
- [订单数据库](#订单数据库) (5张表)
  - [orders](#orders)
  - [order_items](#order_items)

## 订单数据库

### orders
**表描述**: 订单主表

**表信息**:
- 存储引擎: InnoDB
- 字符集: utf8mb4_general_ci
- 估算行数: 10000
- 表大小: 2.5 MB

**字段信息**:
| 序号 | 字段名 | 类型 | 长度 | 允许空值 | 默认值 | 键类型 | 额外 | 注释 |
|------|--------|------|------|----------|--------|--------|------|------|
| 1 | id | bigint | 20 | 否 |  | 主键 | 自增 | 订单ID |
| 2 | order_no | varchar | 32 | 否 |  | 唯一键 |  | 订单号 |
| 3 | user_id | bigint | 20 | 否 |  | 索引 |  | 用户ID |
| 4 | status | tinyint | 4 | 否 | 0 |  |  | 订单状态 |
| 5 | created_at | datetime | - | 否 | CURRENT_TIMESTAMP |  |  | 创建时间 |
```

这个功能现在完全支持动态数据源，您可以通过API传入任意数量的数据源信息，无需在配置文件中预配置。同时保持了向后兼容性，原有的配置文件数据源方式仍然可用。
