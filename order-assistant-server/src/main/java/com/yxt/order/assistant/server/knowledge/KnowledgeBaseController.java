package com.yxt.order.assistant.server.knowledge;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.knowledge.req.DataSetReq;
import com.yxt.order.assistant.server.knowledge.req.DeleteDocumentReq;
import com.yxt.order.assistant.server.knowledge.req.UploadDocumentReq;
import com.yxt.order.assistant.server.knowledge.service.KnowledgeService;
import com.yxt.starter.controller.AbstractController;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.exception.DifyApiException;
import io.github.imfangs.dify.client.model.datasets.DatasetResponse;
import java.io.IOException;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/knowledge/data-set")
public class DataSetController extends AbstractController {

  @Resource
  private DifyDatasetsClient difyDatasetsClient;

  @Resource
  private KnowledgeService knowledgeService;


  /**
   * 获取知识库详情
   * @param req
   * @return
   * @throws DifyApiException
   * @throws IOException
   */
  @PostMapping("/detail")
  public ResponseBase<DatasetResponse> detail(@RequestBody DataSetReq req)
      throws DifyApiException, IOException {
    DatasetResponse dataset = difyDatasetsClient.getDataset(req.getDataSetId());
    return generateSuccess(dataset);
  }


  /**
   * 上传文档到知识库
   * @param req
   * @return
   * @throws DifyApiException
   * @throws IOException
   */
  @PostMapping("/upload")
  public ResponseBase<Boolean> uploadToGroup(@RequestBody UploadDocumentReq req){
    knowledgeService.uploadToGroup(req);
    return generateSuccess(Boolean.TRUE);
  }

  /**
   * 删除知识库所有文档
   */
  @PostMapping("/delete")
  public ResponseBase<Boolean> delete(@RequestBody DeleteDocumentReq req){
    knowledgeService.delete(req);
    return generateSuccess(Boolean.TRUE);
  }

}
