package com.yxt.order.assistant.server.knowledge.database;


import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseColumnInfo;
import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import com.yxt.order.assistant.server.repository.entity.KnowledgeGroup.DataSourceConfig;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 基于JDBC的数据库模式查询服务
 */
@Slf4j
@Service
public class JdbcDatabaseSchemaService {

  @Resource
  private JdbcConnectionManager connectionManager;

  private final ExecutorService executorService = Executors.newFixedThreadPool(10);

  /**
   * 获取多个数据源的表信息
   */
  public List<DatabaseTableInfo> getTablesInfo(List<DataSourceConfig> dataSourceConfigs) {
    List<DatabaseTableInfo> allTables = new ArrayList<>();

    if (dataSourceConfigs == null || dataSourceConfigs.isEmpty()) {
      log.warn("数据源配置列表为空");
      return allTables;
    }

    // 验证数据源配置
    List<DataSourceConfig> validConfigs = dataSourceConfigs.stream().filter(config -> {
      if (!config.isValid()) {
        log.warn("数据源配置无效: {}", config.getName());
        return false;
      }
      return true;
    }).collect(Collectors.toList());

    if (validConfigs.isEmpty()) {
      log.warn("没有有效的数据源配置");
      throw new RuntimeException("没有有效的数据源配置");
    }

    // 串行处理
    for (DataSourceConfig config : validConfigs) {
      try {
        List<DatabaseTableInfo> tables = getTablesInfoFromSingleDataSource(config);
        allTables.addAll(tables);
      } catch (Exception e) {
        log.error("获取数据源 {} 的表信息失败", config.getName(), e);
      }
    }

    log.info("共获取到 {} 张表的信息", allTables.size());
    return allTables;
  }

  /**
   * 获取单个数据源的表信息
   */
  public List<DatabaseTableInfo> getTablesInfoFromSingleDataSource(DataSourceConfig config) {
    List<DatabaseTableInfo> tables = new ArrayList<>();

    log.info("开始获取数据源 {} 的表信息", config.getName());

    // 测试连接
    if (config.getTestConnection() && !connectionManager.testConnection(config)) {
      log.error("数据源 {} 连接测试失败", config.getName());
      return tables;
    }

    try (Connection connection = connectionManager.getConnection(config)) {
      String schemaName = config.getDatabaseName();

      // 查询表基本信息
      String tableQuery = buildTableQuery();

      try (PreparedStatement stmt = connection.prepareStatement(tableQuery)) {
        stmt.setString(1, schemaName);

        try (ResultSet rs = stmt.executeQuery()) {
          while (rs.next()) {
            DatabaseTableInfo tableInfo = new DatabaseTableInfo();
            tableInfo.setDataSourceName(config.getName());
            tableInfo.setSchemaName(schemaName);
            tableInfo.setTableName(rs.getString("TABLE_NAME"));
            tableInfo.setTableComment(rs.getString("TABLE_COMMENT"));
            tableInfo.setTableType(rs.getString("TABLE_TYPE"));
            tableInfo.setEngine(rs.getString("ENGINE"));
            tableInfo.setTableCollation(rs.getString("TABLE_COLLATION"));
            tableInfo.setCreateTime(rs.getString("CREATE_TIME"));
            tableInfo.setUpdateTime(rs.getString("UPDATE_TIME"));

            // 处理可能为null的数值字段
            try {
              tableInfo.setTableRows(rs.getLong("TABLE_ROWS"));
            } catch (SQLException e) {
              tableInfo.setTableRows(0L);
            }

            try {
              tableInfo.setDataLength(rs.getLong("DATA_LENGTH"));
            } catch (SQLException e) {
              tableInfo.setDataLength(0L);
            }

            try {
              tableInfo.setIndexLength(rs.getLong("INDEX_LENGTH"));
            } catch (SQLException e) {
              tableInfo.setIndexLength(0L);
            }

            // 获取字段信息
            List<DatabaseColumnInfo> columns = getColumnsInfo(connection, schemaName,
                tableInfo.getTableName());
            tableInfo.setColumns(columns);

            tables.add(tableInfo);
          }
        }
      }

      log.info("数据源 {} 共获取到 {} 张表", config.getName(), tables.size());

    } catch (SQLException e) {
      log.error("获取数据源 {} 的表信息失败", config.getName(), e);
      throw new RuntimeException("获取数据源表信息失败: " + config.getName(), e);
    }

    return tables;
  }

  /**
   * 构建表查询SQL
   */
  private String buildTableQuery() {
    StringBuilder sql = new StringBuilder();
    sql.append("SELECT ");
    sql.append("TABLE_NAME, ");
    sql.append("TABLE_COMMENT, ");
    sql.append("TABLE_TYPE, ");
    sql.append("ENGINE, ");
    sql.append("TABLE_COLLATION, ");
    sql.append("CREATE_TIME, ");
    sql.append("UPDATE_TIME, ");
    sql.append("TABLE_ROWS, ");
    sql.append("DATA_LENGTH, ");
    sql.append("INDEX_LENGTH ");
    sql.append("FROM information_schema.TABLES ");
    sql.append("WHERE TABLE_SCHEMA = ? ");
    sql.append("AND TABLE_TYPE = 'BASE TABLE' ");
    sql.append("ORDER BY TABLE_NAME");
    return sql.toString();
  }

  /**
   * 获取表的字段信息
   */
  private List<DatabaseColumnInfo> getColumnsInfo(Connection connection, String schemaName,
      String tableName) throws SQLException {
    List<DatabaseColumnInfo> columns = new ArrayList<>();

    String columnQuery = "SELECT " + "COLUMN_NAME, " + "DATA_TYPE, " + "CHARACTER_MAXIMUM_LENGTH, "
        + "NUMERIC_PRECISION, " + "NUMERIC_SCALE, " + "IS_NULLABLE, " + "COLUMN_DEFAULT, "
        + "COLUMN_COMMENT, " + "COLUMN_KEY, " + "EXTRA, " + "ORDINAL_POSITION "
        + "FROM information_schema.COLUMNS " + "WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? "
        + "ORDER BY ORDINAL_POSITION";

    try (PreparedStatement stmt = connection.prepareStatement(columnQuery)) {
      stmt.setString(1, schemaName);
      stmt.setString(2, tableName);

      try (ResultSet rs = stmt.executeQuery()) {
        while (rs.next()) {
          DatabaseColumnInfo columnInfo = new DatabaseColumnInfo();
          columnInfo.setColumnName(rs.getString("COLUMN_NAME"));
          columnInfo.setDataType(rs.getString("DATA_TYPE"));

          // 处理可能为null的字段
          try {
            columnInfo.setCharacterMaximumLength(rs.getLong("CHARACTER_MAXIMUM_LENGTH"));
          } catch (SQLException e) {
            columnInfo.setCharacterMaximumLength(null);
          }

          try {
            columnInfo.setNumericPrecision(rs.getInt("NUMERIC_PRECISION"));
          } catch (SQLException e) {
            columnInfo.setNumericPrecision(null);
          }

          try {
            columnInfo.setNumericScale(rs.getInt("NUMERIC_SCALE"));
          } catch (SQLException e) {
            columnInfo.setNumericScale(null);
          }

          columnInfo.setIsNullable(rs.getString("IS_NULLABLE"));
          columnInfo.setColumnDefault(rs.getString("COLUMN_DEFAULT"));
          columnInfo.setColumnComment(rs.getString("COLUMN_COMMENT"));
          columnInfo.setColumnKey(rs.getString("COLUMN_KEY"));
          columnInfo.setExtra(rs.getString("EXTRA"));
          columnInfo.setOrdinalPosition(rs.getInt("ORDINAL_POSITION"));

          columns.add(columnInfo);
        }
      }
    }

    return columns;
  }

  /**
   * 关闭服务时清理资源
   */
  public void shutdown() {
    executorService.shutdown();
    connectionManager.closeAllDataSources();
  }
}
