package com.yxt.order.assistant.server.knowledge.service;

import com.yxt.order.assistant.server.knowledge.req.DeleteDocumentReq;
import com.yxt.order.assistant.server.knowledge.req.StartPullDatabaseReq;
import com.yxt.order.assistant.server.knowledge.req.UploadDocumentReq;
import com.yxt.order.assistant.server.repository.entity.KnowledgeGroup;

public interface KnowledgeService {

  KnowledgeGroup detail(Long id);

  void pullCfKnowledge(KnowledgeGroup knowledgeGroup);

  void pullSwaggerKnowledge(Long knowledgeGroupId, String swaggerUrl);

  void pullDatabaseKnowledge(StartPullDatabaseReq req);

  void uploadToGroup(UploadDocumentReq req);

  void delete(DeleteDocumentReq req);
}
