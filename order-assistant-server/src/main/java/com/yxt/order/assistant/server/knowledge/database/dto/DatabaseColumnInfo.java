package com.yxt.order.assistant.server.knowledge.database.dto;

import lombok.Data;

/**
 * 数据库字段信息
 */
@Data
public class DatabaseColumnInfo {
    
    /**
     * 字段名
     */
    private String columnName;
    
    /**
     * 字段类型
     */
    private String dataType;
    
    /**
     * 字段长度
     */
    private Long characterMaximumLength;
    
    /**
     * 数值精度
     */
    private Integer numericPrecision;
    
    /**
     * 数值小数位数
     */
    private Integer numericScale;
    
    /**
     * 是否允许为空 YES/NO
     */
    private String isNullable;
    
    /**
     * 默认值
     */
    private String columnDefault;
    
    /**
     * 字段注释
     */
    private String columnComment;
    
    /**
     * 字段键类型 PRI/UNI/MUL
     */
    private String columnKey;
    
    /**
     * 额外信息 如auto_increment
     */
    private String extra;
    
    /**
     * 字段位置
     */
    private Integer ordinalPosition;
    
    /**
     * 获取完整的字段类型描述
     */
    public String getFullDataType() {
        StringBuilder sb = new StringBuilder(dataType);
        
        if (characterMaximumLength != null && characterMaximumLength > 0) {
            sb.append("(").append(characterMaximumLength).append(")");
        } else if (numericPrecision != null && numericPrecision > 0) {
            sb.append("(").append(numericPrecision);
            if (numericScale != null && numericScale > 0) {
                sb.append(",").append(numericScale);
            }
            sb.append(")");
        }
        
        return sb.toString();
    }
    
    /**
     * 是否为主键
     */
    public boolean isPrimaryKey() {
        return "PRI".equals(columnKey);
    }
    
    /**
     * 是否为唯一键
     */
    public boolean isUniqueKey() {
        return "UNI".equals(columnKey);
    }
    
    /**
     * 是否为索引
     */
    public boolean isIndex() {
        return "MUL".equals(columnKey);
    }
    
    /**
     * 是否自增
     */
    public boolean isAutoIncrement() {
        return extra != null && extra.toLowerCase().contains("auto_increment");
    }
}
