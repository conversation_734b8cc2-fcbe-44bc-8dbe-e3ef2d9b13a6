package com.yxt.order.assistant.server.knowledge.database;

import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseColumnInfo;
import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 数据库表信息Markdown导出器
 */
@Slf4j
@Component
public class DatabaseMarkdownExporter {
  /**
   * 生成Markdown文档
   */
  public String generateMarkdown(List<DatabaseTableInfo> allTables) {

    StringBuilder markdown = new StringBuilder();

    // 添加标题和概览
//    markdown.append("# 数据库表信息文档\n\n");
//    markdown.append("本文档包含了所有数据源中的数据库表结构信息。\n\n");

    // 按数据源分组
    Map<String, List<DatabaseTableInfo>> tablesByDataSource = allTables.stream()
        .collect(Collectors.groupingBy(DatabaseTableInfo::getDataSourceName));

    // 生成目录
//    markdown.append("## 目录\n\n");
//    for (String dataSourceName : tablesByDataSource.keySet()) {
//      List<DatabaseTableInfo> tables = tablesByDataSource.get(dataSourceName);
//      markdown.append("- [").append(dataSourceName).append("数据源](#")
//          .append(dataSourceName.toLowerCase()).append("数据源) (")
//          .append(tables.size()).append("张表)\n");
//
//      for (DatabaseTableInfo table : tables) {
//        markdown.append("  - [").append(table.getTableName()).append("](#")
//            .append(table.getTableName().toLowerCase()).append(")\n");
//      }
//    }
//    markdown.append("\n");

    // 生成各数据源的表信息
    for (Map.Entry<String, List<DatabaseTableInfo>> entry : tablesByDataSource.entrySet()) {
      String dataSourceName = entry.getKey();
      List<DatabaseTableInfo> tables = entry.getValue();

//      markdown.append("## ").append(dataSourceName).append("数据源\n\n");
//      markdown.append("数据库: ").append(tables.get(0).getSchemaName()).append("\n");
//      markdown.append("表数量: ").append(tables.size()).append("\n\n");

      for (DatabaseTableInfo table : tables) {
        appendTableInfo(markdown, table);
      }
    }

    log.info("数据库表信息Markdown导出完成，共{}张表", allTables.size());
    return markdown.toString();
  }

  /**
   * 添加单个表的信息到Markdown
   */
  private void appendTableInfo(StringBuilder markdown, DatabaseTableInfo table) {
    // 表标题
//    markdown.append("### ").append(table.getTableName()).append("\n\n");
    markdown.append("## 表名: ").append(table.getTableName()).append("\n\n"); // notey: 较与上面,移除了append("### ")

//    // 表基本信息
//    markdown.append("**表描述**: ").append(StringUtils.hasText(table.getTableComment()) ?
//        table.getTableComment() : "无").append("\n\n");
//
//    // 表统计信息
//    markdown.append("**表信息**:\n");
//    markdown.append("- 存储引擎: ").append(table.getEngine() != null ? table.getEngine() : "未知")
//        .append("\n");
//    markdown.append("- 字符集: ")
//        .append(table.getTableCollation() != null ? table.getTableCollation() : "未知")
//        .append("\n");
//
//    if (table.getTableRows() != null) {
//      markdown.append("- 估算行数: ").append(table.getTableRows()).append("\n");
//    }
//
//    if (table.getTotalSizeMB() != null) {
//      markdown.append("- 表大小: ").append(SIZE_FORMAT.format(table.getTotalSizeMB()))
//          .append(" MB");
//      if (table.getDataSizeMB() != null && table.getIndexSizeMB() != null) {
//        markdown.append(" (数据: ").append(SIZE_FORMAT.format(table.getDataSizeMB()))
//            .append(" MB, 索引: ").append(SIZE_FORMAT.format(table.getIndexSizeMB()))
//            .append(" MB)");
//      }
//      markdown.append("\n");
//    }
//
//    if (StringUtils.hasText(table.getCreateTime())) {
//      markdown.append("- 创建时间: ").append(table.getCreateTime()).append("\n");
//    }
//
//    markdown.append("\n");

    // 字段信息表格
    if (table.getColumns() != null && !table.getColumns().isEmpty()) {
      markdown.append("### 字段信息:\n\n");
      markdown.append(
          "| 序号 | 字段名 | 类型 | 长度 | 允许空值 | 默认值 | 键类型 | 额外 | 注释 |\n");
      markdown.append(
          "|------|--------|------|------|----------|--------|--------|------|------|\n");

      for (DatabaseColumnInfo column : table.getColumns()) {
        markdown.append("| ").append(column.getOrdinalPosition())
            .append(" | ").append(column.getColumnName())
            .append(" | ").append(column.getDataType())
            .append(" | ").append(getColumnLength(column))
            .append(" | ").append("YES".equals(column.getIsNullable()) ? "是" : "否")
            .append(" | ")
            .append(column.getColumnDefault() != null ? column.getColumnDefault() : "")
            .append(" | ").append(getKeyTypeDescription(column))
            .append(" | ").append(getExtraDescription(column))
            .append(" | ")
            .append(column.getColumnComment() != null ? column.getColumnComment() : "")
            .append(" |\n");
      }
      markdown.append("\n");
    }

//    markdown.append("---\n\n");
  }

  /**
   * 获取字段长度描述
   */
  private String getColumnLength(DatabaseColumnInfo column) {
    if (column.getCharacterMaximumLength() != null && column.getCharacterMaximumLength() > 0) {
      return column.getCharacterMaximumLength().toString();
    } else if (column.getNumericPrecision() != null && column.getNumericPrecision() > 0) {
      String precision = column.getNumericPrecision().toString();
      if (column.getNumericScale() != null && column.getNumericScale() > 0) {
        precision += "," + column.getNumericScale();
      }
      return precision;
    }
    return "-";
  }

  /**
   * 获取键类型描述
   */
  private String getKeyTypeDescription(DatabaseColumnInfo column) {
    if (column.isPrimaryKey()) {
      return "主键";
    } else if (column.isUniqueKey()) {
      return "唯一键";
    } else if (column.isIndex()) {
      return "索引";
    }
    return "";
  }

  /**
   * 获取额外信息描述
   */
  private String getExtraDescription(DatabaseColumnInfo column) {
    if (column.isAutoIncrement()) {
      return "自增";
    }
    return column.getExtra() != null ? column.getExtra() : "";
  }
}
