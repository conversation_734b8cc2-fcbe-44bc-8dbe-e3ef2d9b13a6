package com.yxt.order.assistant.server.knowledge.database;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseColumnInfo;
import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.*;
import java.util.*;

/**
 * 数据库模式查询服务
 */
@Slf4j
@Service
public class DatabaseSchemaService {
    
    @Resource
    private DataSource dataSource;
    
    /**
     * 获取所有数据源的表信息
     */
    public List<DatabaseTableInfo> getAllTablesInfo() {
        List<DatabaseTableInfo> allTables = new ArrayList<>();
        
        if (dataSource instanceof DynamicRoutingDataSource) {
            DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) dataSource;
            // 使用反射获取数据源映射，因为getDataSources方法可能不存在
            try {
                java.lang.reflect.Field field = DynamicRoutingDataSource.class.getDeclaredField("dataSourceMap");
                field.setAccessible(true);
                @SuppressWarnings("unchecked")
                Map<String, DataSource> dataSources = (Map<String, DataSource>) field.get(dynamicDataSource);

                for (Map.Entry<String, DataSource> entry : dataSources.entrySet()) {
                    String dataSourceName = entry.getKey();
                    DataSource ds = entry.getValue();

                    try {
                        List<DatabaseTableInfo> tables = getTablesInfo(dataSourceName, ds);
                        allTables.addAll(tables);
                    } catch (Exception e) {
                        log.error("获取数据源 {} 的表信息失败", dataSourceName, e);
                    }
                }
            } catch (Exception e) {
                log.error("无法获取动态数据源列表，使用默认数据源", e);
                try {
                    List<DatabaseTableInfo> tables = getTablesInfo("default", dataSource);
                    allTables.addAll(tables);
                } catch (Exception ex) {
                    log.error("获取默认数据源的表信息失败", ex);
                }
            }
        } else {
            // 单数据源情况
            try {
                List<DatabaseTableInfo> tables = getTablesInfo("default", dataSource);
                allTables.addAll(tables);
            } catch (Exception e) {
                log.error("获取默认数据源的表信息失败", e);
            }
        }
        
        return allTables;
    }
    
    /**
     * 获取指定数据源的表信息
     */
    public List<DatabaseTableInfo> getTablesInfo(String dataSourceName, DataSource ds) throws SQLException {
        List<DatabaseTableInfo> tables = new ArrayList<>();
        
        try (Connection connection = ds.getConnection()) {
            String schemaName = connection.getCatalog();
            
            // 查询表基本信息
            String tableQuery = "SELECT " +
                    "TABLE_NAME, " +
                    "TABLE_COMMENT, " +
                    "TABLE_TYPE, " +
                    "ENGINE, " +
                    "TABLE_COLLATION, " +
                    "CREATE_TIME, " +
                    "UPDATE_TIME, " +
                    "TABLE_ROWS, " +
                    "DATA_LENGTH, " +
                    "INDEX_LENGTH " +
                    "FROM information_schema.TABLES " +
                    "WHERE TABLE_SCHEMA = ? " +
                    "AND TABLE_TYPE = 'BASE TABLE' " +
                    "ORDER BY TABLE_NAME";
            
            try (PreparedStatement stmt = connection.prepareStatement(tableQuery)) {
                stmt.setString(1, schemaName);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        DatabaseTableInfo tableInfo = new DatabaseTableInfo();
                        tableInfo.setDataSourceName(dataSourceName);
                        tableInfo.setSchemaName(schemaName);
                        tableInfo.setTableName(rs.getString("TABLE_NAME"));
                        tableInfo.setTableComment(rs.getString("TABLE_COMMENT"));
                        tableInfo.setTableType(rs.getString("TABLE_TYPE"));
                        tableInfo.setEngine(rs.getString("ENGINE"));
                        tableInfo.setTableCollation(rs.getString("TABLE_COLLATION"));
                        tableInfo.setCreateTime(rs.getString("CREATE_TIME"));
                        tableInfo.setUpdateTime(rs.getString("UPDATE_TIME"));
                        tableInfo.setTableRows(rs.getLong("TABLE_ROWS"));
                        tableInfo.setDataLength(rs.getLong("DATA_LENGTH"));
                        tableInfo.setIndexLength(rs.getLong("INDEX_LENGTH"));
                        
                        // 获取字段信息
                        List<DatabaseColumnInfo> columns = getColumnsInfo(connection, schemaName, tableInfo.getTableName());
                        tableInfo.setColumns(columns);
                        
                        tables.add(tableInfo);
                    }
                }
            }
        }
        
        return tables;
    }
    
    /**
     * 获取表的字段信息
     */
    private List<DatabaseColumnInfo> getColumnsInfo(Connection connection, String schemaName, String tableName) throws SQLException {
        List<DatabaseColumnInfo> columns = new ArrayList<>();
        
        String columnQuery = "SELECT " +
                "COLUMN_NAME, " +
                "DATA_TYPE, " +
                "CHARACTER_MAXIMUM_LENGTH, " +
                "NUMERIC_PRECISION, " +
                "NUMERIC_SCALE, " +
                "IS_NULLABLE, " +
                "COLUMN_DEFAULT, " +
                "COLUMN_COMMENT, " +
                "COLUMN_KEY, " +
                "EXTRA, " +
                "ORDINAL_POSITION " +
                "FROM information_schema.COLUMNS " +
                "WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? " +
                "ORDER BY ORDINAL_POSITION";
        
        try (PreparedStatement stmt = connection.prepareStatement(columnQuery)) {
            stmt.setString(1, schemaName);
            stmt.setString(2, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    DatabaseColumnInfo columnInfo = new DatabaseColumnInfo();
                    columnInfo.setColumnName(rs.getString("COLUMN_NAME"));
                    columnInfo.setDataType(rs.getString("DATA_TYPE"));
                    columnInfo.setCharacterMaximumLength(rs.getLong("CHARACTER_MAXIMUM_LENGTH"));
                    columnInfo.setNumericPrecision(rs.getInt("NUMERIC_PRECISION"));
                    columnInfo.setNumericScale(rs.getInt("NUMERIC_SCALE"));
                    columnInfo.setIsNullable(rs.getString("IS_NULLABLE"));
                    columnInfo.setColumnDefault(rs.getString("COLUMN_DEFAULT"));
                    columnInfo.setColumnComment(rs.getString("COLUMN_COMMENT"));
                    columnInfo.setColumnKey(rs.getString("COLUMN_KEY"));
                    columnInfo.setExtra(rs.getString("EXTRA"));
                    columnInfo.setOrdinalPosition(rs.getInt("ORDINAL_POSITION"));
                    
                    columns.add(columnInfo);
                }
            }
        }
        
        return columns;
    }
}
