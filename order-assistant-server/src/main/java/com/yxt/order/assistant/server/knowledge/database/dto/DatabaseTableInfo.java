package com.yxt.order.assistant.server.knowledge.database.dto;

import lombok.Data;
import java.util.List;

/**
 * 数据库表信息
 */
@Data
public class DatabaseTableInfo {
    
    /**
     * 数据源名称
     */
    private String dataSourceName;
    
    /**
     * 数据库名称
     */
    private String schemaName;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 表注释
     */
    private String tableComment;
    
    /**
     * 表类型 BASE TABLE/VIEW
     */
    private String tableType;
    
    /**
     * 存储引擎
     */
    private String engine;
    
    /**
     * 字符集
     */
    private String tableCollation;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新时间
     */
    private String updateTime;
    
    /**
     * 表的行数（估算）
     */
    private Long tableRows;
    
    /**
     * 数据长度（字节）
     */
    private Long dataLength;
    
    /**
     * 索引长度（字节）
     */
    private Long indexLength;
    
    /**
     * 字段列表
     */
    private List<DatabaseColumnInfo> columns;
    
    /**
     * 获取表的总大小（MB）
     */
    public Double getTotalSizeMB() {
        if (dataLength == null && indexLength == null) {
            return null;
        }
        long total = (dataLength != null ? dataLength : 0) + (indexLength != null ? indexLength : 0);
        return total / 1024.0 / 1024.0;
    }
    
    /**
     * 获取数据大小（MB）
     */
    public Double getDataSizeMB() {
        if (dataLength == null) {
            return null;
        }
        return dataLength / 1024.0 / 1024.0;
    }
    
    /**
     * 获取索引大小（MB）
     */
    public Double getIndexSizeMB() {
        if (indexLength == null) {
            return null;
        }
        return indexLength / 1024.0 / 1024.0;
    }
}
