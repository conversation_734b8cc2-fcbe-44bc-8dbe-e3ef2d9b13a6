package com.yxt.order.assistant.server.knowledge;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.knowledge.req.StartPullCfReq;
import com.yxt.order.assistant.server.knowledge.req.StartPullDatabaseReq;
import com.yxt.order.assistant.server.knowledge.req.StartPullSwaggerReq;
import com.yxt.order.assistant.server.knowledge.service.KnowledgeService;
import com.yxt.order.assistant.server.repository.KnowledgeGroupRepository;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/knowledge")
public class KnowledgeController extends AbstractController {

  @Resource
  private KnowledgeGroupRepository knowledgeGroupRepository;

  @Resource
  private KnowledgeService knowledgeService;


  /**
   * 获取所有支持库(订单助手自有的)
   *
   * @return
   */
  @PostMapping("/list-all-knowledge")
  public ResponseBase<List<KnowledgeBase>> listAllKnowledge() {
    LambdaQueryWrapper<KnowledgeBase> query = new LambdaQueryWrapper<>();
    return generateSuccess(knowledgeGroupRepository.selectList(query));
  }

  /**
   * 启动拉取cf知识库
   *
   * @return
   */
  @PostMapping("/start-pull-cf")
  public ResponseBase<String> startPullCf(@RequestBody StartPullCfReq req) {
    Long knowledgeBaseId = req.getKnowledgeBaseId();

    KnowledgeBase knowledgeGroup = knowledgeService.detail(knowledgeBaseId);
    String extendJson = knowledgeGroup.getExtendJson();
    if (StringUtils.isEmpty(extendJson)) {
      throw new RuntimeException(String.format("%s 拓展配置不能为空", knowledgeGroup.getName()));
    }
    knowledgeService.pullCfKnowledge(knowledgeGroup);
    return generateSuccess("拉取成功");
  }

  /**
   * 启动拉取Swagger API文档
   *
   * @param req 请求参数
   * @return 响应结果
   */
  @PostMapping("/start-pull-swagger")
  public ResponseBase<String> startPullSwagger(@RequestBody StartPullSwaggerReq req) {
    knowledgeService.pullSwaggerKnowledge(req.getKnowledgeBaseId(), req.getSwaggerUrl());
    return generateSuccess("Swagger文档拉取成功");
  }

  /**
   * 启动拉取数据库表信息到知识库
   *
   * @param req 拉取请求
   * @return 响应结果
   */
  @PostMapping("/start-pull-database")
  public ResponseBase<String> startPullDatabase(@RequestBody StartPullDatabaseReq req) {
    Long knowledgeBaseId = req.getKnowledgeBaseId();

    KnowledgeBase knowledgeGroup = knowledgeService.detail(knowledgeBaseId);

    knowledgeService.pullDatabaseKnowledge(knowledgeGroup);
    return generateSuccess("数据库表信息拉取成功");
  }


}
