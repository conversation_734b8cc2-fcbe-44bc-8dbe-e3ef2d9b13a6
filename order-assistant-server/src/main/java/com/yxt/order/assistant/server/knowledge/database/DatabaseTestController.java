package com.yxt.order.assistant.server.knowledge.database;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import com.yxt.starter.controller.AbstractController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据库表信息测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/database-test")
public class DatabaseTestController extends AbstractController {
    
    @Resource
    private DatabaseSchemaService databaseSchemaService;
    
    @Resource
    private DatabaseMarkdownExporter databaseMarkdownExporter;
    
    /**
     * 测试获取所有表信息
     */
    @GetMapping("/tables")
    public ResponseBase<List<DatabaseTableInfo>> getAllTables() {
        try {
            List<DatabaseTableInfo> tables = databaseSchemaService.getAllTablesInfo();
            return generateSuccess(tables);
        } catch (Exception e) {
            log.error("获取表信息失败", e);
            throw new RuntimeException("获取表信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 测试导出Markdown
     */
    @GetMapping("/markdown")
    public ResponseBase<String> exportMarkdown() {
        try {
            String markdown = databaseMarkdownExporter.exportAllTablesToMarkdown();
            return generateSuccess(markdown);
        } catch (Exception e) {
            log.error("导出Markdown失败", e);
            throw new RuntimeException("导出Markdown失败: " + e.getMessage(), e);
        }
    }
}
