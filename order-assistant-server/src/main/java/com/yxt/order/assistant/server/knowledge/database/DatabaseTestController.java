package com.yxt.order.assistant.server.knowledge.database;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.knowledge.database.dto.DataSourceConfig;
import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import com.yxt.order.assistant.server.knowledge.req.StartPullDatabaseReq;
import com.yxt.order.assistant.server.knowledge.service.KnowledgeService;
import com.yxt.order.assistant.server.repository.entity.KnowledgeGroup;
import com.yxt.starter.controller.AbstractController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据库表信息测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/database-test")
public class DatabaseTestController extends AbstractController {

    @Resource
    private DatabaseSchemaService databaseSchemaService;

    @Resource
    private DatabaseMarkdownExporter databaseMarkdownExporter;

    @Resource
    private JdbcDatabaseSchemaService jdbcDatabaseSchemaService;

    @Resource
    private JdbcConnectionManager connectionManager;

    @Resource
    private KnowledgeService knowledgeService;
    
    /**
     * 测试获取配置文件数据源的表信息
     */
    @GetMapping("/tables")
    public ResponseBase<List<DatabaseTableInfo>> getAllTables() {
        try {
            List<DatabaseTableInfo> tables = databaseSchemaService.getAllTablesInfoFromConfiguredDataSources();
            return generateSuccess(tables);
        } catch (Exception e) {
            log.error("获取表信息失败", e);
            throw new RuntimeException("获取表信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 测试动态数据源获取表信息
     */
    @PostMapping("/tables-dynamic")
    public ResponseBase<List<DatabaseTableInfo>> getTablesFromDynamicDataSources(@RequestBody StartPullDatabaseReq req) {
        try {
            KnowledgeGroup detail = knowledgeService.detail(req.getKnowledgeGroupId());
            List<DatabaseTableInfo> tables = databaseSchemaService.getTablesInfo(detail);
            return generateSuccess(tables);
        } catch (Exception e) {
            log.error("获取动态数据源表信息失败", e);
            throw new RuntimeException("获取动态数据源表信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 测试导出配置文件数据源的Markdown
     */
    @GetMapping("/markdown")
    public ResponseBase<String> exportMarkdown() {
        try {
            String markdown = databaseMarkdownExporter.exportAllTablesToMarkdown();
            return generateSuccess(markdown);
        } catch (Exception e) {
            log.error("导出Markdown失败", e);
            throw new RuntimeException("导出Markdown失败: " + e.getMessage(), e);
        }
    }

    /**
     * 测试动态数据源导出Markdown
     */
    @PostMapping("/markdown-dynamic")
    public ResponseBase<String> exportMarkdownFromDynamicDataSources(@RequestBody StartPullDatabaseReq req) {
        try {
            KnowledgeGroup detail = knowledgeService.detail(req.getKnowledgeGroupId());
            String markdown = databaseMarkdownExporter.exportTablesToMarkdown(detail);
            return generateSuccess(markdown);
        } catch (Exception e) {
            log.error("导出动态数据源Markdown失败", e);
            throw new RuntimeException("导出动态数据源Markdown失败: " + e.getMessage(), e);
        }
    }

    /**
     * 测试数据源连接
     */
    @PostMapping("/test-connection")
    public ResponseBase<String> testConnection(@RequestBody DataSourceConfig config) {
        try {
            boolean success = connectionManager.testConnection(config);
            if (success) {
                return generateSuccess("连接测试成功");
            } else {
                return generateSuccess("连接测试失败");
            }
        } catch (Exception e) {
            log.error("测试连接失败", e);
            throw new RuntimeException("测试连接失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取连接池统计信息
     */
    @GetMapping("/connection-stats")
    public ResponseBase<String> getConnectionStats() {
        try {
            String stats = connectionManager.getDataSourceStats();
            return generateSuccess(stats);
        } catch (Exception e) {
            log.error("获取连接池统计信息失败", e);
            throw new RuntimeException("获取连接池统计信息失败: " + e.getMessage(), e);
        }
    }
}
