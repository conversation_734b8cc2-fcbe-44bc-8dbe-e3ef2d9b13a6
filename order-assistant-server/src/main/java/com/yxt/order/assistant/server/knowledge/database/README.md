# 数据库表信息导出功能

## 功能概述

该功能可以将项目中配置的多数据源MySQL数据库的所有表信息导出为详细的Markdown文档，方便建立知识库。

## 功能特点

1. **多数据源支持**：自动识别并处理项目中配置的所有数据源
2. **详细表信息**：包含表名、表注释、字段信息、索引信息等
3. **完整字段信息**：字段名、类型、长度、是否允许空值、默认值、注释等
4. **Markdown格式**：生成格式化的Markdown文档，便于阅读和知识库集成
5. **知识库集成**：可直接集成到现有的知识库系统中

## 核心组件

### 1. DatabaseSchemaService
- 负责查询数据库表结构信息
- 支持多数据源自动发现
- 查询表的基本信息和字段详情

### 2. DatabaseMarkdownExporter
- 将数据库表信息转换为Markdown格式
- 生成结构化的文档，包含目录和详细表信息
- 支持表大小、行数等统计信息

### 3. DTO类
- `DatabaseTableInfo`：表信息实体
- `DatabaseColumnInfo`：字段信息实体

## API接口

### 1. 拉取数据库表信息到知识库
```
POST /knowledge/start-pull-database
```

请求体：
```json
{
  "knowledgeGroupId": 1,
  "includeSystemTables": false,
  "tableNamePattern": null,
  "dataSourceName": null
}
```

### 2. 测试接口

#### 获取所有表信息
```
GET /database-test/tables
```

#### 导出Markdown
```
GET /database-test/markdown
```

## 使用方法

### 1. 通过知识库接口使用

1. 确保已创建知识库组
2. 调用 `/knowledge/start-pull-database` 接口
3. 系统会自动将数据库表信息导出为Markdown并保存到知识库

### 2. 通过测试接口使用

1. 调用 `/database-test/tables` 查看原始表信息
2. 调用 `/database-test/markdown` 获取Markdown格式的文档

## 生成的Markdown文档结构

```markdown
# 数据库表信息文档

## 目录
- [数据源1](#数据源1) (X张表)
  - [表1](#表1)
  - [表2](#表2)

## 数据源1

### 表1
**表描述**: 用户信息表

**表信息**:
- 存储引擎: InnoDB
- 字符集: utf8mb4_general_ci
- 估算行数: 1000
- 表大小: 2.5 MB

**字段信息**:
| 序号 | 字段名 | 类型 | 长度 | 允许空值 | 默认值 | 键类型 | 额外 | 注释 |
|------|--------|------|------|----------|--------|--------|------|------|
| 1 | id | bigint | 20 | 否 |  | 主键 | 自增 | 主键ID |
| 2 | username | varchar | 50 | 否 |  |  |  | 用户名 |
```

## 配置说明

该功能会自动读取项目中配置的数据源信息：

```yaml
spring:
  datasource:
    dynamic:
      primary: dscloud_order_assistant
      datasource:
        dscloud_order_assistant:
          url: ********************************
          username: user
          password: password
        dscloud_offline:
          url: ********************************
          username: user
          password: password
```

## 注意事项

1. 确保数据库连接正常
2. 确保有足够的权限查询 `information_schema` 表
3. 大型数据库可能需要较长时间处理
4. 生成的Markdown文档可能较大，建议分批处理

## 扩展功能

可以通过修改 `StartPullDatabaseReq` 参数来实现：
- 过滤特定表名模式
- 指定特定数据源
- 包含或排除系统表
