package com.yxt.order.assistant.server.knowledge.req;

import lombok.Data;

/**
 * 启动数据库表信息拉取请求
 */
@Data
public class StartPullDatabaseReq {
    
    /**
     * 知识库组ID
     */
    private Long knowledgeGroupId;
    
    /**
     * 是否包含系统表（默认false，只包含业务表）
     */
    private Boolean includeSystemTables = false;
    
    /**
     * 表名过滤模式（可选）
     * 支持通配符，如 "user_*" 表示以user_开头的表
     */
    private String tableNamePattern;
    
    /**
     * 指定数据源名称（可选）
     * 如果不指定，则导出所有数据源的表信息
     */
    private String dataSourceName;
}
