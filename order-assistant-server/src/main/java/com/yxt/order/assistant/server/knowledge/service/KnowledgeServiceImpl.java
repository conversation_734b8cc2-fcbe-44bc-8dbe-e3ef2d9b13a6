package com.yxt.order.assistant.server.knowledge.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.assistant.server.knowledge.confluence.ConfluenceMarkdownExporter;
import com.yxt.order.assistant.server.knowledge.database.DatabaseMarkdownExporter;
import com.yxt.order.assistant.server.knowledge.swagger.SwaggerMarkdownExporter;
import com.yxt.order.assistant.server.knowledge.req.DeleteDocumentReq;
import com.yxt.order.assistant.server.knowledge.req.StartPullDatabaseReq;
import com.yxt.order.assistant.server.knowledge.req.UploadDocumentReq;
import com.yxt.order.assistant.server.repository.KnowledgeGroupRepository;
import com.yxt.order.assistant.server.repository.KnowledgeRepository;
import com.yxt.order.assistant.server.repository.entity.Knowledge;
import com.yxt.order.assistant.server.repository.entity.KnowledgeGroup;
import com.yxt.order.assistant.server.repository.entity.KnowledgeGroup.ExtendJson;
import com.yxt.order.assistant.server.repository.enums.KnowledgeUploadStatus;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.exception.DifyApiException;
import io.github.imfangs.dify.client.model.datasets.CreateDocumentByTextRequest;
import io.github.imfangs.dify.client.model.datasets.DocumentResponse;
import io.github.imfangs.dify.client.model.datasets.ProcessRule;
import io.github.imfangs.dify.client.model.datasets.RetrievalModel;
import io.github.imfangs.dify.client.model.datasets.UpdateDocumentByTextRequest;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class KnowledgeServiceImpl implements KnowledgeService {

  @Resource
  private DifyDatasetsClient difyDatasetsClient;

  @Resource
  private ConfluenceMarkdownExporter confluenceMarkdownExporter;

  @Resource
  private SwaggerMarkdownExporter swaggerMarkdownExporter;

  @Resource
  private KnowledgeGroupRepository knowledgeGroupRepository;

  @Resource
  private KnowledgeRepository knowledgeRepository;

  @Override
  public KnowledgeGroup detail(Long id) {
    LambdaQueryWrapper<KnowledgeGroup> query = new LambdaQueryWrapper<>();
    query.eq(KnowledgeGroup::getId, id);
    KnowledgeGroup knowledgeGroup = knowledgeGroupRepository.selectOne(query);
    if (knowledgeGroup == null) {
      throw new RuntimeException("知识库不存在");
    }
    return knowledgeGroup;
  }

  @Override
  public void pullCfKnowledge(KnowledgeGroup knowledgeGroup) {
    ExtendJson extendJson = knowledgeGroup.fetchCfExtendJson();
    confluenceMarkdownExporter.pullPageAndChildrenToMarkdown(knowledgeGroup.getId(), extendJson);
  }

  @Override
  public void pullSwaggerKnowledge(Long knowledgeGroupId, String swaggerUrl) {
    swaggerMarkdownExporter.exportSwaggerToMarkdown(knowledgeGroupId, swaggerUrl);
  }

  @Override
  public void uploadToGroup(UploadDocumentReq req) {
    Long knowledgeGroupId = req.getKnowledgeGroupId();
    KnowledgeGroup detail = detail(knowledgeGroupId);
    ExtendJson extendJson = detail.fetchDataSetExtendJson();
    String dataSetId = extendJson.getDataSetId();
    Asserts.notEmpty(dataSetId, "dataSetId不能为空");

    LambdaQueryWrapper<Knowledge> query = new LambdaQueryWrapper<>();
    query.eq(Knowledge::getKnowledgeGroupId, detail.getId());
    List<Knowledge> knowledgeList = knowledgeRepository.selectList(query);
    if (CollectionUtils.isEmpty(knowledgeList)) {
      log.info("知识库内容为空,不需要上传");
      return;
    }

    for (Knowledge knowledge : knowledgeList) {
      String mappingRemoteId = knowledge.getMappingRemoteId();
      try {
        if (StringUtils.isEmpty(mappingRemoteId)) {
          addDocument(knowledge, dataSetId);
          log.info("knowledge:id:{} 新增文档成功", knowledge.getId());
        } else {
          updateDocument(knowledge, dataSetId, mappingRemoteId);
          log.info("knowledge:id:{} 更新文档成功", knowledge.getId());
        }

        knowledge.setUploadStatus(KnowledgeUploadStatus.SUCCESS.name());
      } catch (Exception e) {
        knowledge.setUploadStatus(KnowledgeUploadStatus.FAIL.name());
        knowledge.setError(e.getMessage());
        e.printStackTrace();
      }
      knowledgeRepository.updateById(knowledge);
    }
  }


  private void updateDocument(Knowledge knowledge, String dataSetId, String mappingRemoteId)
      throws IOException, DifyApiException {
    UpdateDocumentByTextRequest request = UpdateDocumentByTextRequest.builder()
        .name(knowledge.getTargetName()).text(knowledge.getContent()).build();
    difyDatasetsClient.updateDocumentByText(dataSetId, mappingRemoteId, request);
  }

  private void addDocument(Knowledge knowledge, String dataSetId)
      throws IOException, DifyApiException {
    RetrievalModel retrievalModel = new RetrievalModel();
    retrievalModel.setSearchMethod("hybrid_search");
    retrievalModel.setRerankingEnable(false);
    retrievalModel.setTopK(2);
    retrievalModel.setScoreThresholdEnabled(false);
    CreateDocumentByTextRequest request = CreateDocumentByTextRequest.builder()
        .name(knowledge.getTargetName()).text(knowledge.getContent())
        .indexingTechnique("high_quality").docForm("text_model")
        // 1.1.3 invalid_param (400) - Must not be null! 【doc_language】
        .docLanguage("Chinese")
        // 1.1.3 invalid_param (400) - Must not be null! 【retrieval_model】
        .retrievalModel(retrievalModel)
        // 没有这里的设置，会500报错，服务器内部错误
        .processRule(ProcessRule.builder().mode("automatic").build()).build();

    DocumentResponse documentByText = difyDatasetsClient.createDocumentByText(dataSetId, request);
    // 如果在拉取的时候没有处理好 mappingRemoteId,这里在创建的时候维护
    String id = documentByText.getDocument().getId();
    knowledge.setMappingRemoteId(id);
  }

  @Override
  public void delete(DeleteDocumentReq req) {
    Long knowledgeGroupId = req.getKnowledgeGroupId();
    KnowledgeGroup detail = detail(knowledgeGroupId);
    ExtendJson extendJson = detail.fetchCfExtendJson();
    String dataSetId = extendJson.getDataSetId();
    Asserts.notEmpty(dataSetId, "dataSetId不能为空");

    LambdaQueryWrapper<Knowledge> query = new LambdaQueryWrapper<>();
    query.eq(Knowledge::getKnowledgeGroupId, detail.getId());
    List<Knowledge> knowledgeList = knowledgeRepository.selectList(query);
    if (CollectionUtils.isEmpty(knowledgeList)) {
      log.info("知识库内容为空");
      return;
    }

    for (Knowledge knowledge : knowledgeList) {
      try {
        difyDatasetsClient.deleteDocument(dataSetId, knowledge.getMappingRemoteId());
        knowledgeRepository.deleteById(knowledge.getId());
        log.info("知识删除:{} , 标题: {}",knowledge.getId(),knowledge.getTargetName());
      } catch (Exception e) {
        knowledge.setError(e.getMessage());
      }

      knowledgeRepository.updateById(knowledge);
    }
  }
}
