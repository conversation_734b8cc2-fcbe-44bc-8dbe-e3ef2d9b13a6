server:
  port: 8084
api:
  version: 1.0
compensator:
  enabled: true
spring:
  profiles:
    active: localff
    robot-send: true
  application:
    name: order-assistant
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: 10.4.3.219:8848
        namespace: fca9f3f3-c2a7-49ad-a3ea-d5fe988ceb30
        metadata:
          department: NR
        register-enabled: true
  datasource:
    dynamic:
      primary: dscloud_order_assistant
      strict: false
      datasource:
        dscloud_order_assistant:
          url: **************************************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
      datasource:
        dscloud_order_assistant:
          url: **************************************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 40
      min-idle: 40
      max-active: 100
      max-wait: 10000
      wall:
        multi-statement-allow: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure

  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB


management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
  endpoints:
    web:
      exposure:
        include: ["*"]
  health:
    elasticsearch:
      enabled: false
    mongo:
      enabled: false
  metrics:
    distribution:
      percentiles-histogram[http.server.requests]: true
      maximum-expected-value[http.server.requests]: 10000 #预期最大值
      minimum-expected-value[http.server.requests]: 1 #预期最小值

swagger:
  enable: true

feign:
  hystrix:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      customer-config:
        connectTimeout: 6000
        readTimeout: 6000
        loggerLevel: full
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            strategy: SEMAPHORE
            timeoutInMilliseconds: 60000

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-value: 0
      logic-not-delete-value: 1
      update-strategy: not_null

alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: 17710036783,17302856015

logging:
  level:
    root: info
    com.yxt.order.assistant.order.mapper: debug


grey:
  enable: true
  local-mappings:
    '[(.+)]': $1.svc.k8s.test.hxyxt.com
    order-atom-service: 127.0.0.1:8080

order:
  assistant:
    dify:
      base-url: http://dev-dify.hxyxt.com/v1
      api-key: dataset-P7pSuS6cXpuK06vXPedmKzhT
    cf-black-list-config:
      page-id-list:
        - 73435143
        - 73433644
      title-list:
        - 归档文档回收
        - 4.6.2 团队周会
        - 4.4 研发全流程
      ignore-like:
        - checklist
        - 上线清单模板
        - 技术方案模板
    cf-pull-write-file: false